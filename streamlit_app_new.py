import streamlit as st
import requests
import pandas as pd
import io
import matplotlib.pyplot as plt
from datetime import datetime, date

def main():
    st.title("🏥 设施利用状况分析系统")
    
    # 侧边栏选择功能
    st.sidebar.title("功能选择")
    page = st.sidebar.selectbox(
        "选择功能",
        ["设施数据分析", "缺席记录管理", "稼働率分析", "用户出席情况", "预定vs实绩对比", "问答系统"]
    )

    if page == "设施数据分析":
        show_facility_analysis()
    elif page == "缺席记录管理":
        show_absence_management()
    elif page == "稼働率分析":
        show_occupancy_analysis()
    elif page == "用户出席情况":
        show_user_attendance()
    elif page == "预定vs实绩对比":
        show_plan_vs_actual_analysis()
    elif page == "问答系统":
        show_qa_system()

def show_facility_analysis():
    st.header("📊 设施数据分析")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # 触发分析并下载Excel
        if st.button('一键分析并下载Excel'):
            with st.spinner('分析中...'):
                resp = requests.post('http://localhost:8000/agent/analyze_facility')
                if resp.status_code == 200:
                    st.success('分析完成，点击下方按钮下载Excel！')
                    st.download_button('下载分析结果Excel', resp.content, file_name='facility_analysis_result.xlsx')
                else:
                    st.error('分析失败：' + resp.text)
    
    with col2:
        # 获取新版本的详细分析
        if st.button('获取详细日统计分析'):
            with st.spinner('获取数据中...'):
                resp = requests.post('http://localhost:8000/agent/analyze_facility_new')
                if resp.status_code == 200:
                    data = resp.json()
                    # 将数据保存到session state
                    st.session_state['facility_data'] = data
                    st.success('数据获取成功！')
                else:
                    st.error('获取数据失败：' + resp.text)

        # 检查是否有保存的数据
        if 'facility_data' in st.session_state:
            data = st.session_state['facility_data']

            # 显示汇总信息
            st.subheader("📈 月度汇总")
            summary_data = []
            for facility_name, facility_info in data.items():
                summary_data.append({
                    '设施名称': facility_name,
                    '总用户数': facility_info.get('总用户数', 0),
                    '月度总实绩': facility_info.get('月度总实绩', 0),
                    '月度总缺席': facility_info.get('月度总缺席', 0)
                })

            summary_df = pd.DataFrame(summary_data)
            st.dataframe(summary_df)

            # 可视化
            st.subheader("📊 可视化分析")
            st.bar_chart(summary_df.set_index('设施名称')[['月度总实绩', '月度总缺席']])

            # 显示选定设施的日统计
            st.subheader("📅 日统计详情")
            facility_names = list(data.keys())
            selected_facility = st.selectbox("选择设施查看日统计", facility_names, key="facility_selector")

            if selected_facility and '日统计' in data[selected_facility]:
                daily_stats = data[selected_facility]['日统计']
                daily_df = pd.DataFrame(daily_stats).T
                st.dataframe(daily_df)

                # 检查是否有数据可以绘制图表
                if not daily_df.empty and '实绩人数' in daily_df.columns and '缺席人数' in daily_df.columns:
                    st.line_chart(daily_df[['实绩人数', '缺席人数']])
                else:
                    st.info("该设施暂无可视化数据")

def show_absence_management():
    st.header("📋 缺席记录管理")
    
    # 日期选择
    col1, col2 = st.columns(2)
    with col1:
        date_from = st.date_input("开始日期", value=date(2025, 4, 1))
    with col2:
        date_to = st.date_input("结束日期", value=date(2025, 4, 30))
    
    if st.button("获取缺席记录"):
        with st.spinner('获取缺席记录中...'):
            resp = requests.post('http://localhost:8000/agent/get_absence_records', 
                               json={
                                   'date_from': date_from.strftime('%Y-%m-%d'),
                                   'date_to': date_to.strftime('%Y-%m-%d')
                               })
            if resp.status_code == 200:
                data = resp.json()
                st.success(f'获取到 {data["total_count"]} 条缺席记录')
                
                if data['absence_records']:
                    df = pd.DataFrame(data['absence_records'])
                    st.dataframe(df)
                    
                    # 按设施统计缺席情况
                    facility_absence = df.groupby('facility')['absence_count'].sum().reset_index()
                    st.subheader("各设施缺席统计")
                    st.bar_chart(facility_absence.set_index('facility')['absence_count'])
                else:
                    st.info("该日期范围内没有缺席记录")
            else:
                st.error('获取缺席记录失败：' + resp.text)

def show_occupancy_analysis():
    st.header("📈 稼働率分析")
    
    if st.button("获取稼働率分析"):
        with st.spinner('分析稼働率中...'):
            resp = requests.post('http://localhost:8000/agent/get_occupancy_rate')
            if resp.status_code == 200:
                data = resp.json()
                st.success('稼働率分析完成！')
                
                # 转换为DataFrame
                df_data = []
                for facility_name, metrics in data.items():
                    row = {'设施名称': facility_name}
                    row.update(metrics)
                    df_data.append(row)
                
                df = pd.DataFrame(df_data)
                st.dataframe(df)
                
                # 稼働率可视化
                st.subheader("稼働率对比")
                occupancy_df = df.set_index('设施名称')[['计划稼働率', '实绩稼働率']]
                st.bar_chart(occupancy_df)
                
                # 取消率分析
                st.subheader("取消率分析")
                cancel_df = df.set_index('设施名称')['取消率']
                st.bar_chart(cancel_df)
                
            else:
                st.error('获取稼働率失败：' + resp.text)

def show_user_attendance():
    st.header("👤 用户出席情况")

    # 田上预定表中的用户列表
    tagami_users = [
        '厚地 知子', '新井 輝夫', '石場 イツヱ', '今次 フサ枝', '岡元 米子', '竹本 シゲ子',
        '鳥越 カズエ', '仲川 哲生', '永江 甲一', '松元 水惠', '松山 トシ', '宮城 久子',
        '泉 茂美治', '今村 美智子', '今村 良治', '岡﨑 三規雄', '髙橋 和男', '船間 渡',
        '益田 雄二', '岡積 直樹', '長山 裕子', '坂 光弘', '平田 晃', '村田 マツエ',
        '山下 千賀子', '山下 玲子'
    ]

    # 用户和日期选择
    col1, col2, col3 = st.columns(3)
    with col1:
        user_name = st.selectbox("选择用户", sorted(set(tagami_users)), key="user_selector")
    with col2:
        date_from = st.date_input("开始日期", value=date(2025, 4, 1), key="user_date_from")
    with col3:
        date_to = st.date_input("结束日期", value=date(2025, 4, 30), key="user_date_to")
    
    if st.button("获取用户出席情况"):
        with st.spinner('获取用户出席情况中...'):
            resp = requests.post('http://localhost:8000/agent/get_user_attendance',
                               json={
                                   'user_name': user_name,
                                   'date_from': date_from.strftime('%Y-%m-%d'),
                                   'date_to': date_to.strftime('%Y-%m-%d')
                               })
            if resp.status_code == 200:
                data = resp.json()
                # 将数据保存到session state
                st.session_state['user_attendance_data'] = data
                st.success(f'获取用户 {data["user_name"]} 的出席情况')
            else:
                st.error('获取用户出席情况失败：' + resp.text)

    # 检查是否有保存的数据
    if 'user_attendance_data' in st.session_state:
        data = st.session_state['user_attendance_data']

        # 显示统计信息
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("计划出席天数", data['plan_count'])
        with col2:
            st.metric("实际出席天数", data['attend_count'])
        with col3:
            st.metric("稼働率", f"{data['occupancy_rate']:.2%}")

        # 显示详细出席情况
        st.subheader("详细出席记录")
        attendance_data = []
        for date_str, info in data['attendance_info'].items():
            attendance_data.append({
                '日期': date_str,
                '计划出席': '是' if info['plan'] else '否',
                '实际出席': '是' if info['actual'] else '否',
                '状态': '正常出席' if info['plan'] and info['actual']
                       else '缺席' if info['plan'] and not info['actual']
                       else '额外出席' if not info['plan'] and info['actual']
                       else '无计划'
            })

        attendance_df = pd.DataFrame(attendance_data)
        st.dataframe(attendance_df, use_container_width=True)

        # 添加可视化图表
        st.subheader("📊 出席情况可视化")

        # 统计各种状态的天数
        status_counts = attendance_df['状态'].value_counts()
        if not status_counts.empty:
            st.bar_chart(status_counts)

        # 显示缺席的具体日期
        absent_days = attendance_df[attendance_df['状态'] == '缺席']
        if not absent_days.empty:
            st.subheader("⚠️ 缺席日期详情")
            st.dataframe(absent_days[['日期', '状态']], use_container_width=True)
        else:
            st.success("✅ 该用户在选定期间内没有缺席记录！")

def show_qa_system():
    st.header("🤖 AI问答系统")
    
    # 获取结构化JSON并可视化
    if st.button('获取结构化分析并可视化'):
        with st.spinner('获取数据中...'):
            resp = requests.post('http://localhost:8000/agent/analyze_facility_json')
            if resp.status_code == 200:
                data = resp.json()
                st.write('后端返回原始数据：', data)  # debug输出
                df = pd.DataFrame(data).T
                st.dataframe(df)
                # 容错：如无"缺席人数"列则补0
                if '缺席人数' not in df.columns:
                    df['缺席人数'] = 0
                st.bar_chart(df['缺席人数'])
                # 容错：如无"取消覆盖率"列则补0
                if '取消覆盖率' not in df.columns:
                    df['取消覆盖率'] = 0
                st.bar_chart(df['取消覆盖率'])
                # AI智能解读
                prompt = f"请用简洁中文总结以下据点数据的主要风险和建议：{df.to_dict()}"
                qa_resp = requests.post('http://localhost:8000/agent/qa', json={"question": prompt})
                if qa_resp.status_code == 200:
                    st.markdown('### AI智能解读')
                    st.write(qa_resp.json().get('answer', '无解读'))
                else:
                    st.warning('AI解读失败')
            else:
                st.error('获取数据失败：' + resp.text)

def show_plan_vs_actual_analysis():
    st.header("🔍 预定vs实绩对比分析")

    # 添加新的预定vs实绩对比分析
    if st.button("🔍 预定vs实绩对比分析（田上）"):
        with st.spinner("正在进行预定vs实绩对比分析..."):
            try:
                response = requests.post("http://localhost:8000/agent/analyze_with_plan_data")
                if response.status_code == 200:
                    result = response.json()
                    # 将数据保存到session state
                    st.session_state['plan_vs_actual_data'] = result
                    st.success("✅ 预定vs实绩对比分析完成！")
                else:
                    st.error(f"分析失败: {response.status_code}")
                    st.text(response.text)
            except Exception as e:
                st.error(f"请求失败: {str(e)}")

    # 检查是否有保存的数据
    if 'plan_vs_actual_data' in st.session_state:
        result = st.session_state['plan_vs_actual_data']

        # 显示汇总信息
        st.subheader("📊 田上设施分析汇总")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("月度总计划", f"{result['summary']['月度总计划']}人次")
        with col2:
            st.metric("月度总实绩", f"{result['summary']['月度总实绩']}人次")
        with col3:
            st.metric("月度总缺席", f"{result['summary']['月度总缺席']}人次")
        with col4:
            st.metric("缺席率", f"{result['summary']['缺席率']}%")

        # 显示日统计
        st.subheader("📅 每日统计详情")

        # 创建DataFrame用于显示
        daily_data = []
        for day, stats in result['日统计'].items():
            daily_data.append({
                '日期': f"4月{day}日",
                '计划人数': stats['计划人数'],
                '实绩人数': stats['实绩人数'],
                '缺席人数': stats['缺席人数'],
                '出席率': f"{(stats['实绩人数'] / stats['计划人数'] * 100):.1f}%" if stats['计划人数'] > 0 else "0%"
            })

        df_daily = pd.DataFrame(daily_data)
        st.dataframe(df_daily, use_container_width=True)

        # 可视化图表
        st.subheader("📈 缺席趋势图")

        # 准备图表数据
        days = [int(day) for day in result['日统计'].keys()]
        planned = [result['日统计'][str(day)]['计划人数'] for day in days]
        actual = [result['日统计'][str(day)]['实绩人数'] for day in days]
        absent = [result['日统计'][str(day)]['缺席人数'] for day in days]

        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 6))

        ax.plot(days, planned, label='计划人数', marker='o', linewidth=2)
        ax.plot(days, actual, label='实绩人数', marker='s', linewidth=2)
        ax.bar(days, absent, alpha=0.3, label='缺席人数', color='red')

        ax.set_xlabel('日期')
        ax.set_ylabel('人数')
        ax.set_title('田上设施 - 预定vs实绩对比（4月）')
        ax.legend()
        ax.grid(True, alpha=0.3)

        st.pyplot(fig)

        # 显示缺席最多的日期
        st.subheader("⚠️ 缺席较多的日期")
        high_absence_days = [(day, stats) for day, stats in result['日统计'].items()
                           if stats['缺席人数'] > 2]

        if high_absence_days:
            for day, stats in sorted(high_absence_days, key=lambda x: x[1]['缺席人数'], reverse=True):
                st.warning(f"**4月{day}日**: 计划{stats['计划人数']}人，实绩{stats['实绩人数']}人，缺席{stats['缺席人数']}人")

                # 显示计划用户列表
                with st.expander(f"查看4月{day}日计划用户列表"):
                    planned_users = stats.get('计划用户', [])
                    if planned_users:
                        st.write("**计划用户：**")
                        for i, user in enumerate(planned_users, 1):
                            st.write(f"{i}. {user}")
                    else:
                        st.write("无计划用户数据")
        else:
            st.info("✅ 没有缺席人数超过2人的日期")

        # 显示29日的详细信息（你特别关心的日期）
        st.subheader("🔍 29日详细分析")
        if '29' in result['日统计']:
            day_29_stats = result['日统计']['29']
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("29日计划人数", f"{day_29_stats['计划人数']}人")
            with col2:
                st.metric("29日实绩人数", f"{day_29_stats['实绩人数']}人")
            with col3:
                st.metric("29日缺席人数", f"{day_29_stats['缺席人数']}人")

            # 显示29日计划用户
            with st.expander("查看29日计划用户详情"):
                planned_users_29 = day_29_stats.get('计划用户', [])
                if planned_users_29:
                    st.write("**29日计划用户列表：**")
                    for i, user in enumerate(planned_users_29, 1):
                        st.write(f"{i}. {user}")
                else:
                    st.write("无29日计划用户数据")
        else:
            st.warning("未找到29日数据")

if __name__ == "__main__":
    main()
