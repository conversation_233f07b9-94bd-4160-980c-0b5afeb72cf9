import streamlit as st
import requests
import pandas as pd
import io

st.title('デイ振替ツール（AI・自動化実験版）')

# 1. 触发Agent自动下载数据
if st.button('一键自动下载カイポケ预定/实绩数据'):
    with st.spinner('Agent正在自动下载数据...'):
        resp = requests.post('http://localhost:8000/api/run_download_agent')
        if resp.status_code == 200:
            st.success('下载完成！')
        else:
            st.error('下载失败：' + resp.text)

# 2. 触发Agent自动处理数据
if st.button('一键处理并刷新今日数据'):
    with st.spinner('Agent正在处理数据...'):
        resp = requests.post('http://localhost:8000/api/run_processing_agent')
        if resp.status_code == 200:
            st.success('数据处理完成！')
        else:
            st.error('处理失败：' + resp.text)

# 3. 展示今日实绩、预定、缺席人员
st.header('今日各据点实绩/预定/缺席一览')
resp = requests.get('http://localhost:8000/api/today_status')
if resp.status_code == 200:
    data = resp.json()
    st.write('后端返回原始数据：', data)
    for facility, info in data.items():
        st.subheader(facility)
        st.write('预定人数:', info.get('scheduled_count', 0))
        st.write('实绩人数:', info.get('actual_count', 0))
        st.write('缺席人员:')
        if info.get('absences'):
            df_abs = pd.DataFrame(info['absences'])
            st.dataframe(df_abs)
            # 缺席补录
            for idx, row in df_abs.iterrows():
                with st.expander(f"补录: {row['user_name']} {row['date']}"):
                    reason = st.text_input(f"缺席原因（{row['user_name']}）", value=row.get('reason',''), key=f"reason_{facility}_{idx}")
                    can_reschedule = st.selectbox(f"可否调整日期（{row['user_name']}）", ['不可', '可'], index=1 if row.get('can_reschedule') else 0, key=f"can_{facility}_{idx}")
                    rescheduled_date = ''
                    if can_reschedule == '可':
                        rescheduled_date = st.date_input(f"调整后日期（{row['user_name']}）", key=f"date_{facility}_{idx}")
                    if st.button(f"提交补录（{row['user_name']}）", key=f"submit_{facility}_{idx}"):
                        payload = {
                            'facility_name': facility,
                            'date': row['date'],
                            'user_name': row['user_name'],
                            'reason': reason,
                            'can_reschedule': can_reschedule == '可',
                            'rescheduled_date': str(rescheduled_date) if can_reschedule == '可' else None
                        }
                        r = requests.post('http://localhost:8000/api/absences/update', json=payload)
                        if r.status_code == 200:
                            st.success('补录成功！')
                        else:
                            st.error('补录失败：' + r.text)
        else:
            st.write('无缺席人员')
else:
    st.error('获取今日数据失败：' + resp.text)
